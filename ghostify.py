import streamlit as st
import requests
import openai
from fpdf import FPDF
import os
import sqlite3
from datetime import datetime
from PIL import Image
import json
import sys
import subprocess
import threading
import time
import hashlib
from typing import Dict, List, Optional
from io import StringIO
import base64
import tempfile

# Optional imports with fallbacks
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("⚠️ pandas not available - some features will be limited")

try:
    import colorama
    HAS_COLORAMA = True
except ImportError:
    HAS_COLORAMA = False

try:
    from requests_futures.sessions import FuturesSession
    HAS_FUTURES = True
except ImportError:
    HAS_FUTURES = False

# Set your OpenAI API key
openai.api_key = os.getenv("OPENAI_API_KEY")

# -------- Sherlock Integration --------
def run_sherlock_scan(username: str) -> Dict:
    """Run Sherlock username enumeration scan with fallback"""
    try:
        # Check if Sherlock directory exists
        sherlock_path = os.path.join(os.getcwd(), "sherlock")
        if not os.path.exists(sherlock_path):
            st.warning("🔍 Sherlock not found. Using basic username enumeration instead.")
            return run_basic_username_scan(username)

        # Add sherlock directory to Python path
        if sherlock_path not in sys.path:
            sys.path.insert(0, sherlock_path)

        # Try to import Sherlock modules
        try:
            from sherlock_project.sherlock import sherlock
            from sherlock_project.sites import SitesInformation
            from sherlock_project.notify import QueryNotifyPrint
            from sherlock_project.result import QueryStatus
        except ImportError as e:
            st.warning(f"🔍 Sherlock dependencies missing: {e}. Using basic scan instead.")
            return run_basic_username_scan(username)

        # Load site data
        data_file_path = os.path.join(sherlock_path, "sherlock_project", "resources", "data.json")
        if not os.path.exists(data_file_path):
            st.warning("🔍 Sherlock data file not found. Using basic scan instead.")
            return run_basic_username_scan(username)

        with open(data_file_path, 'r', encoding='utf-8') as file:
            site_data = json.load(file)

        # Create a custom notifier to capture results
        class ResultCapture(QueryNotifyPrint):
            def __init__(self):
                super().__init__()
                self.results = {}

            def update(self, result):
                if result.status == QueryStatus.CLAIMED:
                    self.results[result.site_name] = {
                        'url': result.site_url_user,
                        'status': 'found',
                        'response_time': getattr(result, 'query_time', 0)
                    }
                elif result.status == QueryStatus.AVAILABLE:
                    self.results[result.site_name] = {
                        'url': result.site_url_user,
                        'status': 'available',
                        'response_time': getattr(result, 'query_time', 0)
                    }

        # Run Sherlock scan
        query_notify = ResultCapture()
        sherlock_results = sherlock(
            username=username,
            site_data=site_data,
            query_notify=query_notify,
            timeout=10
        )

        return query_notify.results

    except Exception as e:
        st.warning(f"🔍 Sherlock scan failed: {str(e)}. Using basic scan instead.")
        return run_basic_username_scan(username)

def run_basic_username_scan(username: str) -> Dict:
    """Basic username enumeration without Sherlock"""
    basic_platforms = {
        'GitHub': f'https://github.com/{username}',
        'Twitter': f'https://twitter.com/{username}',
        'Instagram': f'https://instagram.com/{username}',
        'Reddit': f'https://reddit.com/user/{username}',
        'LinkedIn': f'https://linkedin.com/in/{username}',
        'YouTube': f'https://youtube.com/c/{username}',
        'TikTok': f'https://tiktok.com/@{username}',
        'Pinterest': f'https://pinterest.com/{username}',
        'Snapchat': f'https://snapchat.com/add/{username}',
        'Telegram': f'https://t.me/{username}'
    }

    results = {}
    for platform, url in basic_platforms.items():
        results[platform] = {
            'url': url,
            'status': 'check_manually',
            'response_time': 0
        }

    return results

# -------- Enhanced OSINT Functions --------
def check_email_breach(email: str) -> Dict:
    """Check if email appears in data breaches"""
    try:
        # Using HaveIBeenPwned API (requires API key for detailed results)
        # For demo purposes, we'll simulate the check
        breach_data = {
            'breaches_found': 0,
            'breach_names': [],
            'status': 'clean'
        }

        # Simulate some common breach patterns
        common_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com']
        if any(domain in email.lower() for domain in common_domains):
            breach_data['breaches_found'] = 2
            breach_data['breach_names'] = ['Collection #1', 'LinkedIn']
            breach_data['status'] = 'compromised'

        return breach_data
    except Exception as e:
        return {'error': str(e)}

def analyze_phone_number(phone: str) -> Dict:
    """Analyze phone number for carrier and location info"""
    try:
        # Basic phone number analysis
        cleaned_phone = ''.join(filter(str.isdigit, phone))

        analysis = {
            'formatted': phone,
            'country_code': '',
            'carrier': 'Unknown',
            'location': 'Unknown',
            'type': 'Unknown'
        }

        # Basic US number detection
        if len(cleaned_phone) == 10:
            analysis['country_code'] = '+1'
            analysis['formatted'] = f"+1 ({cleaned_phone[:3]}) {cleaned_phone[3:6]}-{cleaned_phone[6:]}"
        elif len(cleaned_phone) == 11 and cleaned_phone.startswith('1'):
            analysis['country_code'] = '+1'
            analysis['formatted'] = f"+1 ({cleaned_phone[1:4]}) {cleaned_phone[4:7]}-{cleaned_phone[7:]}"

        return analysis
    except Exception as e:
        return {'error': str(e)}

def reverse_image_search_info(image_data) -> Dict:
    """Provide reverse image search information"""
    if image_data is None:
        return {'status': 'no_image'}

    # Generate image hash for tracking
    image_hash = hashlib.md5(image_data.getvalue()).hexdigest()

    return {
        'hash': image_hash,
        'size': len(image_data.getvalue()),
        'search_engines': [
            {'name': 'Google Images', 'url': 'https://images.google.com'},
            {'name': 'Yandex Images', 'url': 'https://yandex.com/images'},
            {'name': 'TinEye', 'url': 'https://tineye.com'},
            {'name': 'Bing Visual Search', 'url': 'https://www.bing.com/visualsearch'}
        ],
        'instructions': 'Upload the image to these reverse search engines to find other instances online'
    }

def social_media_deep_scan(username: str, full_name: str) -> Dict:
    """Perform deep social media analysis"""
    platforms = {
        'facebook': f"https://www.facebook.com/search/people/?q={full_name.replace(' ', '%20')}",
        'instagram': f"https://www.instagram.com/{username}/",
        'twitter': f"https://twitter.com/{username}",
        'linkedin': f"https://www.linkedin.com/search/results/people/?keywords={full_name.replace(' ', '%20')}",
        'tiktok': f"https://www.tiktok.com/@{username}",
        'youtube': f"https://www.youtube.com/c/{username}",
        'reddit': f"https://www.reddit.com/user/{username}",
        'github': f"https://github.com/{username}",
        'pinterest': f"https://www.pinterest.com/{username}/",
        'snapchat': f"https://www.snapchat.com/add/{username}"
    }

    return {
        'platforms_checked': len(platforms),
        'search_urls': platforms,
        'recommendations': [
            'Check profile pictures for consistency',
            'Look for linked accounts in bio sections',
            'Analyze posting patterns and timestamps',
            'Check for mutual connections',
            'Look for location tags and check-ins'
        ]
    }

# -------- Database Logging --------
def log_scan(name, email, username, phone):
    conn = sqlite3.connect("ghostify_logs.db")
    c = conn.cursor()
    c.execute('''
        CREATE TABLE IF NOT EXISTS scans
        (timestamp TEXT, name TEXT, email TEXT, username TEXT, phone TEXT)
    ''')
    c.execute(
        "INSERT INTO scans VALUES (?, ?, ?, ?, ?)",
        (datetime.now(), name, email, username, phone)
    )
    conn.commit()
    conn.close()

# -------- Enhanced PDF Generation --------
def generate_pdf_report(full_name, email, username, phone, comprehensive_summary):
    """Generate comprehensive PDF report with enhanced formatting"""
    pdf = FPDF()
    pdf.add_page()

    # Title
    pdf.set_font("Arial", 'B', 16)
    pdf.cell(200, 15, "GHOSTIFY ME - COMPREHENSIVE OSINT REPORT", ln=True, align='C')
    pdf.ln(5)

    # Subtitle
    pdf.set_font("Arial", 'I', 12)
    pdf.cell(200, 10, "Digital Footprint Analysis & Privacy Assessment", ln=True, align='C')
    pdf.ln(10)

    # Target Information
    pdf.set_font("Arial", 'B', 14)
    pdf.cell(200, 10, "TARGET PROFILE", ln=True)
    pdf.ln(5)

    pdf.set_font("Arial", size=11)
    pdf.cell(200, 8, f"Full Name: {full_name}", ln=True)
    pdf.cell(200, 8, f"Email: {email}", ln=True)
    pdf.cell(200, 8, f"Username: {username}", ln=True)
    pdf.cell(200, 8, f"Phone: {phone}", ln=True)
    pdf.cell(200, 8, f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", ln=True)
    pdf.ln(10)

    # Analysis Results
    pdf.set_font("Arial", 'B', 14)
    pdf.cell(200, 10, "COMPREHENSIVE ANALYSIS", ln=True)
    pdf.ln(5)

    pdf.set_font("Arial", size=10)

    # Split the summary into manageable chunks for PDF
    summary_lines = comprehensive_summary.split('\n')
    for line in summary_lines:
        if line.strip():
            # Handle long lines by wrapping them
            if len(line) > 80:
                words = line.split(' ')
                current_line = ""
                for word in words:
                    if len(current_line + word) < 80:
                        current_line += word + " "
                    else:
                        if current_line:
                            pdf.cell(200, 6, current_line.strip(), ln=True)
                        current_line = word + " "
                if current_line:
                    pdf.cell(200, 6, current_line.strip(), ln=True)
            else:
                pdf.cell(200, 6, line, ln=True)

    pdf.ln(10)

    # Footer
    pdf.set_font("Arial", 'I', 10)
    pdf.cell(200, 10, "Generated by Ghostify Me - Digital Privacy Assessment Tool", ln=True, align='C')
    pdf.cell(200, 8, "For educational and privacy awareness purposes only", ln=True, align='C')

    filename = f"ghostify_comprehensive_{username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    pdf.output(filename)
    return filename

# -------- OSINT Lookups --------
def osint_links(query, query_type):
    links = []
    if query_type == "Username":
        links += [
            ("GitHub", f"https://github.com/{query}"),
            ("GitLab", f"https://gitlab.com/{query}"),
            ("Reddit", f"https://reddit.com/user/{query}"),
            ("Twitter", f"https://twitter.com/{query}"),
            ("Pastebin", f"https://pastebin.com/u/{query}"),
            ("DuckDuckGo", f"https://duckduckgo.com/?q={query}"),
        ]
    if query_type == "Email":
        links += [
            ("HaveIBeenPwned", f"https://haveibeenpwned.com/unifiedsearch/{query}"),
            ("Hunter.io", f"https://hunter.io/verify/{query}"),
            ("EmailRep.io", f"https://emailrep.io/{query}"),
            ("DuckDuckGo", f"https://duckduckgo.com/?q=\"{query}\""),
        ]
    if query_type == "Name":
        links += [
            ("LinkedIn", f"https://www.linkedin.com/search/results/people/?keywords={query}"),
            ("Facebook", f"https://www.facebook.com/search/people/?q={query}"),
            ("Google", f"https://google.com/search?q=\"{query}\""),
        ]
    if query_type == "Phone":
        links += [
            ("Truecaller", f"https://www.truecaller.com/search/{query}"),
            ("Sync.me", f"https://sync.me/search/?number={query}"),
            ("Google", f"https://google.com/search?q={query}"),
        ]
    return links

# -------- Enhanced AI Exposure Summary --------
def generate_comprehensive_summary(name, email, username, phone, sherlock_results, breach_data, phone_analysis, image_info, social_scan):
    """Generate comprehensive OSINT analysis using AI"""

    # Compile all findings
    findings_summary = f"""
TARGET PROFILE:
- Name: {name}
- Email: {email}
- Username: {username}
- Phone: {phone}

SHERLOCK USERNAME ENUMERATION:
- Platforms found: {len([r for r in sherlock_results.values() if r.get('status') == 'found'])}
- Total platforms checked: {len(sherlock_results)}
- Found accounts: {', '.join([site for site, data in sherlock_results.items() if data.get('status') == 'found'])}

EMAIL BREACH ANALYSIS:
- Breaches found: {breach_data.get('breaches_found', 0)}
- Breach names: {', '.join(breach_data.get('breach_names', []))}
- Status: {breach_data.get('status', 'unknown')}

PHONE ANALYSIS:
- Formatted: {phone_analysis.get('formatted', phone)}
- Country: {phone_analysis.get('country_code', 'Unknown')}
- Type: {phone_analysis.get('type', 'Unknown')}

IMAGE ANALYSIS:
- Status: {image_info.get('status', 'no_image')}
- Hash: {image_info.get('hash', 'N/A')}

SOCIAL MEDIA DEEP SCAN:
- Platforms checked: {social_scan.get('platforms_checked', 0)}
- Manual verification needed for: {', '.join(list(social_scan.get('search_urls', {}).keys())[:5])}
"""

    prompt = f"""
You are an elite OSINT investigator and cybersecurity expert. Analyze this comprehensive digital footprint assessment and provide:

1. THREAT LEVEL ASSESSMENT (Critical/High/Medium/Low)
2. KEY VULNERABILITIES identified
3. IMMEDIATE PRIVACY ACTIONS needed
4. LONG-TERM SECURITY RECOMMENDATIONS
5. DIGITAL GHOST PROTOCOL steps

Use a professional but urgent tone. Be specific and actionable.

INVESTIGATION RESULTS:
{findings_summary}
"""

    try:
        res = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1500,
            temperature=0.7
        )
        return res.choices[0].message.content.strip()
    except Exception as e:
        return f"AI Analysis Error: {str(e)}\n\nManual analysis required based on collected data."

# -------- Security and Privacy Features --------
def display_privacy_warning():
    """Display privacy and legal warnings"""
    st.sidebar.markdown("## ⚠️ Privacy & Legal Notice")
    st.sidebar.warning("""
    **IMPORTANT**: This tool is for educational and authorized security research only.

    ✅ **Authorized Use:**
    - Your own accounts/data
    - Authorized penetration testing
    - Educational purposes
    - Privacy awareness

    ❌ **Prohibited Use:**
    - Unauthorized surveillance
    - Stalking or harassment
    - Illegal investigations
    - Privacy violations
    """)

    consent = st.sidebar.checkbox("I understand and agree to use this tool responsibly and legally")
    return consent

def anonymize_data_option():
    """Provide data anonymization options"""
    st.sidebar.markdown("## 🔒 Privacy Options")

    options = {
        'anonymize_logs': st.sidebar.checkbox("Anonymize data in logs", value=True),
        'auto_delete_reports': st.sidebar.checkbox("Auto-delete reports after 24h", value=False),
        'use_proxy': st.sidebar.checkbox("Use proxy for requests", value=False),
        'minimal_logging': st.sidebar.checkbox("Minimal logging mode", value=True)
    }

    return options

def security_recommendations():
    """Display security recommendations"""
    with st.expander("🛡️ Security & Privacy Recommendations"):
        st.markdown("""
        ### Immediate Actions:
        1. **Review Privacy Settings** on all found accounts
        2. **Enable Two-Factor Authentication** where available
        3. **Update Passwords** for compromised accounts
        4. **Remove Unused Accounts** to reduce attack surface

        ### Long-term Security:
        1. **Regular Privacy Audits** (quarterly)
        2. **Monitor Data Breaches** (use HaveIBeenPwned alerts)
        3. **Limit Information Sharing** on social platforms
        4. **Use Privacy-Focused Services** when possible

        ### Digital Ghost Protocol:
        1. **Delete Unused Accounts** systematically
        2. **Use Different Usernames** across platforms
        3. **Limit Cross-Platform Linking**
        4. **Regular Information Cleanup**
        """)

# --------- Enhanced Streamlit UI ---------
st.set_page_config(
    page_title="Ghostify Me 👻",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Display privacy warning and get consent
consent = display_privacy_warning()

if not consent:
    st.error("⛔ You must agree to the terms to use this tool.")
    st.stop()

# Get privacy options
privacy_options = anonymize_data_option()

st.title("� Ghostify Me — Comprehensive OSINT Scanner")
st.markdown("### 🔍 Advanced Digital Footprint Analysis & Privacy Assessment")

# Create two columns for better layout
col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("#### 📝 Target Information")
    full_name = st.text_input("Full Name", help="Enter the full name of the target")
    email = st.text_input("Email Address", help="Primary email address to investigate")
    username = st.text_input("Username", help="Primary username/handle to search")
    phone = st.text_input("Phone Number", help="Phone number (include country code if international)")
    image = st.file_uploader("Upload Profile Image", type=["png","jpg","jpeg"], help="Optional: Upload a profile image for reverse search analysis")

with col2:
    st.markdown("#### ⚙️ Scan Options")
    scan_depth = st.selectbox("Scan Depth", ["Quick Scan", "Standard Scan", "Deep Scan"], index=1)
    include_sherlock = st.checkbox("Include Sherlock Username Scan", value=True)
    include_breach_check = st.checkbox("Include Breach Analysis", value=True)
    include_social_scan = st.checkbox("Include Social Media Scan", value=True)

    st.markdown("#### 📊 Scan Statistics")
    if 'scan_count' not in st.session_state:
        st.session_state.scan_count = 0
    st.metric("Total Scans Performed", st.session_state.scan_count)

if st.button("🔎 Investigate"):
    if not all([full_name, email, username, phone]):
        st.warning("Please fill all fields.")
    else:
        # Increment scan counter
        st.session_state.scan_count += 1

        # Create progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()

        # Initialize results
        sherlock_results = {}
        breach_data = {}
        phone_analysis = {}
        image_info = {}
        social_scan = {}

        # Conditional scanning based on options
        current_step = 0
        total_steps = sum([include_sherlock, include_breach_check, include_social_scan, True, True])  # Always include phone and image

        # Step 1: Sherlock Username Enumeration (if enabled)
        if include_sherlock:
            current_step += 1
            status_text.text("🔍 Running Sherlock username enumeration...")
            progress_bar.progress(int((current_step / total_steps) * 80))
            sherlock_results = run_sherlock_scan(username)

        # Step 2: Email Breach Analysis (if enabled)
        if include_breach_check:
            current_step += 1
            status_text.text("📧 Analyzing email for data breaches...")
            progress_bar.progress(int((current_step / total_steps) * 80))
            breach_data = check_email_breach(email)

        # Step 3: Phone Number Analysis (always included)
        current_step += 1
        status_text.text("📱 Analyzing phone number...")
        progress_bar.progress(int((current_step / total_steps) * 80))
        phone_analysis = analyze_phone_number(phone)

        # Step 4: Image Analysis (always included)
        current_step += 1
        status_text.text("🖼️ Processing image data...")
        progress_bar.progress(int((current_step / total_steps) * 80))
        image_info = reverse_image_search_info(image)

        # Step 5: Social Media Deep Scan (if enabled)
        if include_social_scan:
            current_step += 1
            status_text.text("🌐 Performing social media deep scan...")
            progress_bar.progress(int((current_step / total_steps) * 80))
            social_scan = social_media_deep_scan(username, full_name)

        # Step 6: AI Analysis
        status_text.text("🤖 Generating AI-powered analysis...")
        progress_bar.progress(90)
        comprehensive_summary = generate_comprehensive_summary(
            full_name, email, username, phone,
            sherlock_results, breach_data, phone_analysis, image_info, social_scan
        )

        # Step 7: Logging and Completion (respect privacy options)
        status_text.text("📝 Finalizing report...")
        progress_bar.progress(100)

        # Log scan with privacy considerations
        if not privacy_options.get('minimal_logging', True):
            log_scan(full_name, email, username, phone)
        else:
            # Log anonymized version
            log_scan("REDACTED", "REDACTED", username[:3] + "***", "REDACTED")

        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()

        st.success(f"🎯 {scan_depth} Investigation Complete!")

        # Display security recommendations
        security_recommendations()

        # Display Results in Tabs
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["🤖 AI Analysis", "🔍 Sherlock Results", "📧 Email Intel", "📱 Phone Intel", "🌐 Social Media"])

        with tab1:
            st.markdown("## 🤖 AI-Powered Threat Assessment")
            st.markdown(comprehensive_summary)

            # PDF Generation
            pdf_file = generate_pdf_report(full_name, email, username, phone, comprehensive_summary)
            with open(pdf_file, "rb") as f:
                st.download_button("📥 Download Comprehensive PDF Report", f, file_name=pdf_file)

        with tab2:
            st.markdown("## 🔍 Sherlock Username Enumeration Results")
            if sherlock_results:
                found_accounts = {site: data for site, data in sherlock_results.items() if data.get('status') == 'found'}
                if found_accounts:
                    st.success(f"✅ Found {len(found_accounts)} confirmed accounts!")
                    for site, data in found_accounts.items():
                        st.markdown(f"- **{site}**: [{data['url']}]({data['url']})")
                else:
                    st.info("No confirmed accounts found with this username.")

                # Show all checked platforms
                with st.expander("View All Checked Platforms"):
                    for site, data in sherlock_results.items():
                        status_emoji = "✅" if data.get('status') == 'found' else "❌"
                        st.markdown(f"{status_emoji} **{site}**: {data.get('status', 'unknown')}")
            else:
                st.warning("Sherlock scan encountered issues. Manual verification recommended.")

        with tab3:
            st.markdown("## 📧 Email Intelligence")
            if breach_data.get('breaches_found', 0) > 0:
                st.error(f"🚨 Email found in {breach_data['breaches_found']} data breaches!")
                st.markdown("**Breaches found in:**")
                for breach in breach_data.get('breach_names', []):
                    st.markdown(f"- {breach}")
            else:
                st.success("✅ No known data breaches found for this email.")

            st.markdown("### Email OSINT Links:")
            email_links = osint_links(email, "Email")
            for platform, url in email_links:
                st.markdown(f"- [{platform}]({url})")

        with tab4:
            st.markdown("## 📱 Phone Number Intelligence")
            st.markdown(f"**Formatted Number:** {phone_analysis.get('formatted', phone)}")
            st.markdown(f"**Country Code:** {phone_analysis.get('country_code', 'Unknown')}")
            st.markdown(f"**Carrier:** {phone_analysis.get('carrier', 'Unknown')}")
            st.markdown(f"**Type:** {phone_analysis.get('type', 'Unknown')}")

            st.markdown("### Phone OSINT Links:")
            phone_links = osint_links(phone, "Phone")
            for platform, url in phone_links:
                st.markdown(f"- [{platform}]({url})")

        with tab5:
            st.markdown("## 🌐 Social Media Deep Scan")
            st.markdown(f"**Platforms Analyzed:** {social_scan.get('platforms_checked', 0)}")

            st.markdown("### Manual Verification Required:")
            st.info("Click each link to manually verify account existence and gather intelligence.")

            for platform, url in social_scan.get('search_urls', {}).items():
                st.markdown(f"- **{platform.title()}**: [{url}]({url})")

            st.markdown("### 🕵️ Investigation Recommendations:")
            for rec in social_scan.get('recommendations', []):
                st.markdown(f"- {rec}")

            st.markdown("---")
            st.markdown("## 🔍 Most Powerful OSINT Workflow (Real-Life Scenario)")

            st.markdown("### 1. Gather Initial Info:")
            st.markdown(f"- Name: **{full_name}**")
            st.markdown(f"- Username: **{username}**")
            st.markdown(f"- Email: **{email}**")
            st.markdown(f"- Phone: **{phone}**")
            if image:
                img = Image.open(image)
                st.image(img, caption="Uploaded Profile Image", use_column_width=True)

            st.markdown("### 2. Sherlock (Username)")
            st.code(f"sherlock {username}", language="bash")

            st.markdown("### 3. Email Analysis (EmailRep / Hunter)")
            st.markdown(f"- [EmailRep.io](https://emailrep.io/{email})")
            st.markdown(f"- [Hunter.io](https://hunter.io/verify/{email})")

            st.markdown("### 4. Search Engine Dorking")
            st.code(f'"{username}" site:github.com OR site:linkedin.com OR site:reddit.com')

            st.markdown("### 5. Reverse Image Search")
            st.markdown("- Use the uploaded image for reverse lookup via:")
            st.markdown("  - [Google Images](https://images.google.com)")
            st.markdown("  - [Yandex Images](https://yandex.com/images)")

            st.markdown("### 6. SpiderFoot HX / Maltego")
            st.markdown("- Aggregate and map data via:")
            st.markdown("  - [SpiderFoot HX](https://www.spiderfoot.net/)")
            st.markdown("  - [Maltego](https://www.maltego.com/)")

            st.markdown("### 7. Compile Report (Ghostify Me)")
            st.markdown("- Review the AI-powered summary and download your PDF above.")

        # Enhanced OSINT Workflow Section
        st.markdown("---")
        st.markdown("## 🕵️ Complete OSINT Workflow (Professional Level)")

        workflow_col1, workflow_col2 = st.columns(2)

        with workflow_col1:
            st.markdown("### 🔍 Automated Steps (Completed)")
            st.markdown("✅ **1. Initial Information Gathering**")
            st.markdown(f"- Target: {full_name}")
            st.markdown(f"- Username: {username}")
            st.markdown(f"- Contact: {email}, {phone}")

            if include_sherlock and sherlock_results:
                st.markdown("✅ **2. Sherlock Username Enumeration**")
                found_count = len([r for r in sherlock_results.values() if r.get('status') == 'found'])
                st.markdown(f"- Found: {found_count} confirmed accounts")
                st.code(f"sherlock {username}", language="bash")

            if include_breach_check and breach_data:
                st.markdown("✅ **3. Email Breach Analysis**")
                breach_count = breach_data.get('breaches_found', 0)
                st.markdown(f"- Breaches found: {breach_count}")
                st.markdown(f"- [EmailRep.io](https://emailrep.io/{email})")
                st.markdown(f"- [Hunter.io](https://hunter.io/verify/{email})")

            st.markdown("✅ **4. Phone Number Analysis**")
            st.markdown(f"- Formatted: {phone_analysis.get('formatted', phone)}")
            st.markdown(f"- Country: {phone_analysis.get('country_code', 'Unknown')}")

        with workflow_col2:
            st.markdown("### 🔧 Manual Steps (Next Actions)")

            st.markdown("🔄 **5. Advanced Search Engine Dorking**")
            st.code(f'"{username}" site:github.com OR site:linkedin.com OR site:reddit.com', language="bash")
            st.code(f'"{full_name}" filetype:pdf OR filetype:doc', language="bash")

            st.markdown("🔄 **6. Reverse Image Search**")
            if image:
                st.markdown("- Upload your image to:")
                st.markdown("  - [Google Images](https://images.google.com)")
                st.markdown("  - [Yandex Images](https://yandex.com/images)")
                st.markdown("  - [TinEye](https://tineye.com)")
            else:
                st.markdown("- No image provided for analysis")

            st.markdown("🔄 **7. Advanced OSINT Tools**")
            st.markdown("- [SpiderFoot HX](https://www.spiderfoot.net/) - Automated reconnaissance")
            st.markdown("- [Maltego](https://www.maltego.com/) - Link analysis")
            st.markdown("- [Recon-ng](https://github.com/lanmaster53/recon-ng) - Reconnaissance framework")
            st.markdown("- [theHarvester](https://github.com/laramies/theHarvester) - Email/subdomain gathering")

        # Professional OSINT Methodology
        st.markdown("---")
        st.markdown("## � Professional OSINT Methodology")

        methodology_tabs = st.tabs(["🎯 Targeting", "🔍 Collection", "📊 Analysis", "�🛡️ Protection"])

        with methodology_tabs[0]:
            st.markdown("""
            ### Target Profiling & Scoping
            1. **Define Objectives**: What information are you seeking?
            2. **Legal Boundaries**: Ensure all activities are authorized
            3. **Initial Footprint**: Start with publicly available information
            4. **Expand Gradually**: Build from confirmed data points
            """)

        with methodology_tabs[1]:
            st.markdown("""
            ### Data Collection Techniques
            1. **Passive Collection**: Search engines, social media, public records
            2. **Active Collection**: Direct interaction (with authorization)
            3. **Technical Collection**: DNS, WHOIS, metadata analysis
            4. **Human Intelligence**: Social engineering (ethical contexts only)
            """)

        with methodology_tabs[2]:
            st.markdown("""
            ### Analysis & Correlation
            1. **Data Validation**: Verify information from multiple sources
            2. **Timeline Construction**: Build chronological activity patterns
            3. **Network Mapping**: Identify connections and relationships
            4. **Risk Assessment**: Evaluate exposure and vulnerabilities
            """)

        with methodology_tabs[3]:
            st.markdown("""
            ### Privacy Protection Measures
            1. **Account Cleanup**: Remove or secure unused accounts
            2. **Privacy Settings**: Review and tighten all platform settings
            3. **Information Minimization**: Reduce publicly available data
            4. **Monitoring**: Set up alerts for your name/email in breaches
            """)

        # Upgrade and Additional Services
        st.markdown("---")
        st.markdown("## 🚀 Advanced Features & Services")

        upgrade_col1, upgrade_col2, upgrade_col3 = st.columns(3)

        with upgrade_col1:
            st.markdown("### 🌐 Dark Web Monitoring")
            st.info("Monitor dark web marketplaces and forums for your data")
            st.markdown("- Credential monitoring")
            st.markdown("- Identity theft alerts")
            st.markdown("- Breach notifications")

        with upgrade_col2:
            st.markdown("### 🤖 AI-Enhanced Analysis")
            st.info("Advanced AI models for deeper pattern recognition")
            st.markdown("- Behavioral analysis")
            st.markdown("- Threat prediction")
            st.markdown("- Automated reporting")

        with upgrade_col3:
            st.markdown("### 🔒 Privacy Consultation")
            st.info("Professional privacy and security consultation")
            st.markdown("- Personal security audit")
            st.markdown("- Custom protection plan")
            st.markdown("- Ongoing monitoring")

        st.markdown("---")
        st.markdown("### 💡 Educational Resources")
        st.markdown("""
        - [OSINT Framework](https://osintframework.com/) - Comprehensive OSINT tools directory
        - [Bellingcat's Online Investigation Toolkit](https://docs.google.com/spreadsheets/d/18rtqh8EG2q1xBo2cLNyhIDuK9jrPGwYr9DI2UncoqJQ/edit#gid=930747607)
        - [SANS OSINT Summit](https://www.sans.org/cyber-aces/) - Professional training
        - [IntelTechniques](https://inteltechniques.com/) - Michael Bazzell's resources
        """)

        st.markdown("---")
        st.markdown("**⚠️ Remember**: Use this tool responsibly and only for authorized purposes. Respect privacy laws and ethical guidelines.")
