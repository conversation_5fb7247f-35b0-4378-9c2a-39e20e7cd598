import streamlit as st
import requests
import openai
from fpdf import FPDF
import os
import sqlite3
from datetime import datetime
from PIL import Image

# Set your OpenAI API key
openai.api_key = os.getenv("OPENAI_API_KEY")

# -------- Database Logging --------
def log_scan(name, email, username, phone):
    conn = sqlite3.connect("ghostify_logs.db")
    c = conn.cursor()
    c.execute('''
        CREATE TABLE IF NOT EXISTS scans
        (timestamp TEXT, name TEXT, email TEXT, username TEXT, phone TEXT)
    ''')
    c.execute(
        "INSERT INTO scans VALUES (?, ?, ?, ?, ?)",
        (datetime.now(), name, email, username, phone)
    )
    conn.commit()
    conn.close()

# -------- PDF Generation --------
def generate_pdf_report(full_name, email, username, phone, exposure_summary):
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font("Arial", size=12)

    pdf.cell(200, 10, txt="Ghostify Me Comprehensive OSINT Report", ln=True, align='C')
    pdf.ln(10)

    pdf.cell(200, 10, txt=f"Full Name: {full_name}", ln=True)
    pdf.cell(200, 10, txt=f"Email: {email}", ln=True)
    pdf.cell(200, 10, txt=f"Username: {username}", ln=True)
    pdf.cell(200, 10, txt=f"Phone: {phone}", ln=True)
    pdf.ln(10)

    pdf.multi_cell(200, 10, txt=f"Exposure Summary:\n\n{exposure_summary}")
    pdf.ln(10)
    pdf.cell(200, 10, txt="Generated by Ghostify Me 🕵️", ln=True)

    filename = f"ghostify_{username}.pdf"
    pdf.output(filename)
    return filename

# -------- OSINT Lookups --------
def osint_links(query, query_type):
    links = []
    if query_type == "Username":
        links += [
            ("GitHub", f"https://github.com/{query}"),
            ("GitLab", f"https://gitlab.com/{query}"),
            ("Reddit", f"https://reddit.com/user/{query}"),
            ("Twitter", f"https://twitter.com/{query}"),
            ("Pastebin", f"https://pastebin.com/u/{query}"),
            ("DuckDuckGo", f"https://duckduckgo.com/?q={query}"),
        ]
    if query_type == "Email":
        links += [
            ("HaveIBeenPwned", f"https://haveibeenpwned.com/unifiedsearch/{query}"),
            ("Hunter.io", f"https://hunter.io/verify/{query}"),
            ("EmailRep.io", f"https://emailrep.io/{query}"),
            ("DuckDuckGo", f"https://duckduckgo.com/?q=\"{query}\""),
        ]
    if query_type == "Name":
        links += [
            ("LinkedIn", f"https://www.linkedin.com/search/results/people/?keywords={query}"),
            ("Facebook", f"https://www.facebook.com/search/people/?q={query}"),
            ("Google", f"https://google.com/search?q=\"{query}\""),
        ]
    if query_type == "Phone":
        links += [
            ("Truecaller", f"https://www.truecaller.com/search/{query}"),
            ("Sync.me", f"https://sync.me/search/?number={query}"),
            ("Google", f"https://google.com/search?q={query}"),
        ]
    return links

# -------- AI Exposure Summary --------
def generate_summary(name, email, username, phone):
    types = ["Name", "Email", "Username", "Phone"]
    terms = [name, email, username, phone]
    findings = []
    for t, term in zip(types, terms):
        links = osint_links(term, t)
        findings += [f"- 🔍 [{platform}]({url})" for platform, url in links]

    prompt = f"""
You're an OSINT expert. Given these OSINT search links, write a concise, hacker-style report summarizing this person's online exposure and provide actionable privacy steps:

{"\n".join(findings)}
"""
    res = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}]
    )
    return res.choices[0].message.content.strip()

# --------- Streamlit UI ---------
st.set_page_config(page_title="Ghostify Me 👻", layout="centered")

st.title("🕵️ Ghostify Me — Comprehensive OSINT Scanner")
st.write("Scan and analyze a person's digital footprint comprehensively.")

full_name = st.text_input("Full Name")
email = st.text_input("Email Address")
username = st.text_input("Username")
phone = st.text_input("Phone Number")
image = st.file_uploader("Upload Profile Image", type=["png","jpg","jpeg"])

if st.button("🔎 Investigate"):
    if not all([full_name, email, username, phone]):
        st.warning("Please fill all fields.")
    else:
        with st.spinner("Gathering OSINT data..."):
            summary = generate_summary(full_name, email, username, phone)
            log_scan(full_name, email, username, phone)
            st.success("Investigation complete!")

            st.markdown("## 📋 OSINT Report")
            st.markdown(summary)

            pdf_file = generate_pdf_report(full_name, email, username, phone, summary)
            with open(pdf_file, "rb") as f:
                st.download_button("📥 Download PDF Report", f, file_name=pdf_file)

            st.markdown("---")
            st.markdown("## 🔍 Most Powerful OSINT Workflow (Real-Life Scenario)")

            st.markdown("### 1. Gather Initial Info:")
            st.markdown(f"- Name: **{full_name}**")
            st.markdown(f"- Username: **{username}**")
            st.markdown(f"- Email: **{email}**")
            st.markdown(f"- Phone: **{phone}**")
            if image:
                img = Image.open(image)
                st.image(img, caption="Uploaded Profile Image", use_column_width=True)

            st.markdown("### 2. Sherlock (Username)")
            st.code(f"sherlock {username}", language="bash")

            st.markdown("### 3. Email Analysis (EmailRep / Hunter)")
            st.markdown(f"- [EmailRep.io](https://emailrep.io/{email})")
            st.markdown(f"- [Hunter.io](https://hunter.io/verify/{email})")

            st.markdown("### 4. Search Engine Dorking")
            st.code(f'"{username}" site:github.com OR site:linkedin.com OR site:reddit.com')

            st.markdown("### 5. Reverse Image Search")
            st.markdown("- Use the uploaded image for reverse lookup via:")
            st.markdown("  - [Google Images](https://images.google.com)")
            st.markdown("  - [Yandex Images](https://yandex.com/images)")

            st.markdown("### 6. SpiderFoot HX / Maltego")
            st.markdown("- Aggregate and map data via:")
            st.markdown("  - [SpiderFoot HX](https://www.spiderfoot.net/)")
            st.markdown("  - [Maltego](https://www.maltego.com/)")

            st.markdown("### 7. Compile Report (Ghostify Me)")
            st.markdown("- Review the AI-powered summary and download your PDF above.")

            st.markdown("---")
            st.markdown("## 🛡️ Protect Your Digital Footprint")
            st.markdown("[Upgrade – Advanced Privacy & Dark Web Monitoring](https://buy.stripe.com/test_xxx123)")
